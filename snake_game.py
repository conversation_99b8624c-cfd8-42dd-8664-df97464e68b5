import pygame
import random
import sys
import math

# 初始化pygame
pygame.init()

# 颜色定义
BLACK = (0, 0, 0)
DARK_GREEN = (0, 150, 0)
GREEN = (0, 255, 0)
LIGHT_GREEN = (100, 255, 100)
RED = (255, 0, 0)
WHITE = (255, 255, 255)
BLUE = (0, 100, 255)
GRAY = (128, 128, 128)
YELLOW = (255, 255, 0)

# 游戏设置
WIDTH, HEIGHT = 600, 400
GRID_SIZE = 20
BASE_FPS = 10
MAX_FPS = 20
SPEED_INCREMENT = 0.5

# 游戏状态
GAME_STATE_START = 0
GAME_STATE_PLAYING = 1
GAME_STATE_OVER = 2

# 创建屏幕
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption('贪食蛇')
clock = pygame.time.Clock()

# 蛇类
class Snake:
    def __init__(self):
        self.positions = [(WIDTH//2, HEIGHT//2)]
        self.direction = (GRID_SIZE, 0)
        self.length = 1

    def get_head_position(self):
        return self.positions[0]

    def move(self):
        head = self.get_head_position()
        x, y = self.direction
        new_position = ((head[0] + x) % WIDTH, (head[1] + y) % HEIGHT)

        if new_position in self.positions[1:]:
            return False  # 游戏结束

        self.positions.insert(0, new_position)
        if len(self.positions) > self.length:
            self.positions.pop()
        return True

    def draw(self, surface):
        for i, position in enumerate(self.positions):
            if i == 0:  # 蛇头
                self.draw_head(surface, position)
            elif i == len(self.positions) - 1:  # 蛇尾
                self.draw_tail(surface, position)
            else:  # 蛇身
                self.draw_body(surface, position, i)

    def draw_head(self, surface, position):
        # 绘制蛇头（圆形，深绿色）
        center_x = position[0] + GRID_SIZE // 2
        center_y = position[1] + GRID_SIZE // 2
        pygame.draw.circle(surface, DARK_GREEN, (center_x, center_y), GRID_SIZE // 2)

        # 绘制眼睛
        eye_size = 3
        if self.direction == (GRID_SIZE, 0):  # 向右
            eye1_pos = (center_x + 3, center_y - 4)
            eye2_pos = (center_x + 3, center_y + 4)
        elif self.direction == (-GRID_SIZE, 0):  # 向左
            eye1_pos = (center_x - 3, center_y - 4)
            eye2_pos = (center_x - 3, center_y + 4)
        elif self.direction == (0, -GRID_SIZE):  # 向上
            eye1_pos = (center_x - 4, center_y - 3)
            eye2_pos = (center_x + 4, center_y - 3)
        else:  # 向下
            eye1_pos = (center_x - 4, center_y + 3)
            eye2_pos = (center_x + 4, center_y + 3)

        pygame.draw.circle(surface, WHITE, eye1_pos, eye_size)
        pygame.draw.circle(surface, WHITE, eye2_pos, eye_size)
        pygame.draw.circle(surface, BLACK, eye1_pos, eye_size - 1)
        pygame.draw.circle(surface, BLACK, eye2_pos, eye_size - 1)

    def draw_body(self, surface, position, index):
        # 绘制蛇身（渐变绿色）
        rect = pygame.Rect(position[0], position[1], GRID_SIZE, GRID_SIZE)
        # 根据位置创建渐变效果
        color_intensity = max(150, 255 - index * 10)
        body_color = (0, min(255, color_intensity), 0)
        pygame.draw.rect(surface, body_color, rect)
        # 添加边框
        pygame.draw.rect(surface, DARK_GREEN, rect, 1)

    def draw_tail(self, surface, position):
        # 绘制蛇尾（浅绿色，圆角）
        rect = pygame.Rect(position[0], position[1], GRID_SIZE, GRID_SIZE)
        pygame.draw.rect(surface, LIGHT_GREEN, rect)
        pygame.draw.rect(surface, DARK_GREEN, rect, 1)

# 食物类
class Food:
    def __init__(self):
        self.position = (0, 0)
        self.randomize_position()

    def randomize_position(self):
        self.position = (
            random.randint(0, (WIDTH-GRID_SIZE)//GRID_SIZE) * GRID_SIZE,
            random.randint(0, (HEIGHT-GRID_SIZE)//GRID_SIZE) * GRID_SIZE
        )

    def draw(self, surface):
        # 绘制苹果形状的食物
        center_x = self.position[0] + GRID_SIZE // 2
        center_y = self.position[1] + GRID_SIZE // 2

        # 苹果主体
        pygame.draw.circle(surface, RED, (center_x, center_y), GRID_SIZE // 2 - 2)

        # 苹果叶子
        leaf_points = [
            (center_x - 2, center_y - 8),
            (center_x + 2, center_y - 6),
            (center_x + 4, center_y - 8),
            (center_x, center_y - 4)
        ]
        pygame.draw.polygon(surface, GREEN, leaf_points)

        # 苹果高光
        pygame.draw.circle(surface, WHITE, (center_x - 3, center_y - 3), 2)

# 游戏类
class Game:
    def __init__(self):
        self.state = GAME_STATE_START
        self.snake = Snake()
        self.food = Food()
        self.score = 0
        self.current_fps = BASE_FPS

    def reset_game(self):
        self.snake = Snake()
        self.food = Food()
        self.score = 0
        self.current_fps = BASE_FPS
        self.state = GAME_STATE_PLAYING

    def update_speed(self):
        self.current_fps = min(MAX_FPS, BASE_FPS + self.score * SPEED_INCREMENT)

    def draw_start_screen(self, surface):
        surface.fill(BLACK)

        # 标题
        title_font = pygame.font.SysFont('arial', 48, bold=True)
        title_text = title_font.render('贪食蛇游戏', True, GREEN)
        title_rect = title_text.get_rect(center=(WIDTH//2, HEIGHT//2 - 80))
        surface.blit(title_text, title_rect)

        # 副标题
        subtitle_font = pygame.font.SysFont('arial', 24)
        subtitle_text = subtitle_font.render('Snake Game', True, WHITE)
        subtitle_rect = subtitle_text.get_rect(center=(WIDTH//2, HEIGHT//2 - 40))
        surface.blit(subtitle_text, subtitle_rect)

        # START按钮
        button_rect = pygame.Rect(WIDTH//2 - 60, HEIGHT//2 + 20, 120, 40)
        pygame.draw.rect(surface, BLUE, button_rect)
        pygame.draw.rect(surface, WHITE, button_rect, 2)

        button_font = pygame.font.SysFont('arial', 24, bold=True)
        button_text = button_font.render('START', True, WHITE)
        button_text_rect = button_text.get_rect(center=button_rect.center)
        surface.blit(button_text, button_text_rect)

        # 说明文字
        info_font = pygame.font.SysFont('arial', 16)
        info_lines = [
            '使用方向键控制蛇的移动',
            '吃到红色食物可以增长并加速',
            '不要撞到自己！'
        ]

        for i, line in enumerate(info_lines):
            info_text = info_font.render(line, True, GRAY)
            info_rect = info_text.get_rect(center=(WIDTH//2, HEIGHT//2 + 100 + i * 20))
            surface.blit(info_text, info_rect)

        return button_rect

    def draw_game_over_screen(self, surface):
        # 半透明覆盖层
        overlay = pygame.Surface((WIDTH, HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        surface.blit(overlay, (0, 0))

        # 游戏结束文字
        game_over_font = pygame.font.SysFont('arial', 36, bold=True)
        game_over_text = game_over_font.render('游戏结束!', True, RED)
        game_over_rect = game_over_text.get_rect(center=(WIDTH//2, HEIGHT//2 - 40))
        surface.blit(game_over_text, game_over_rect)

        # 最终分数
        score_font = pygame.font.SysFont('arial', 24)
        score_text = score_font.render(f'最终分数: {self.score}', True, WHITE)
        score_rect = score_text.get_rect(center=(WIDTH//2, HEIGHT//2))
        surface.blit(score_text, score_rect)

        # 重新开始提示
        restart_font = pygame.font.SysFont('arial', 18)
        restart_text = restart_font.render('按 R 重新开始 或 ESC 退出', True, YELLOW)
        restart_rect = restart_text.get_rect(center=(WIDTH//2, HEIGHT//2 + 40))
        surface.blit(restart_text, restart_rect)

# 主游戏函数
def main():
    game = Game()
    start_button_rect = None

    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            elif event.type == pygame.KEYDOWN:
                if game.state == GAME_STATE_START:
                    if event.key == pygame.K_SPACE or event.key == pygame.K_RETURN:
                        game.reset_game()
                elif game.state == GAME_STATE_PLAYING:
                    if event.key == pygame.K_UP and game.snake.direction != (0, GRID_SIZE):
                        game.snake.direction = (0, -GRID_SIZE)
                    elif event.key == pygame.K_DOWN and game.snake.direction != (0, -GRID_SIZE):
                        game.snake.direction = (0, GRID_SIZE)
                    elif event.key == pygame.K_LEFT and game.snake.direction != (GRID_SIZE, 0):
                        game.snake.direction = (-GRID_SIZE, 0)
                    elif event.key == pygame.K_RIGHT and game.snake.direction != (-GRID_SIZE, 0):
                        game.snake.direction = (GRID_SIZE, 0)
                elif game.state == GAME_STATE_OVER:
                    if event.key == pygame.K_r:
                        game.reset_game()
                    elif event.key == pygame.K_ESCAPE:
                        pygame.quit()
                        sys.exit()
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if game.state == GAME_STATE_START and start_button_rect:
                    if start_button_rect.collidepoint(event.pos):
                        game.reset_game()

        # 游戏逻辑更新
        if game.state == GAME_STATE_PLAYING:
            # 移动蛇
            if not game.snake.move():
                game.state = GAME_STATE_OVER
            else:
                # 检查是否吃到食物
                if game.snake.get_head_position() == game.food.position:
                    game.snake.length += 1
                    game.score += 1
                    game.update_speed()

                    # 确保食物不生成在蛇身上
                    while game.food.position in game.snake.positions:
                        game.food.randomize_position()

        # 绘制
        if game.state == GAME_STATE_START:
            start_button_rect = game.draw_start_screen(screen)
        elif game.state == GAME_STATE_PLAYING:
            screen.fill(BLACK)
            game.snake.draw(screen)
            game.food.draw(screen)

            # 显示分数和速度
            font = pygame.font.SysFont('arial', 20)
            score_text = font.render(f'分数: {game.score}', True, WHITE)
            speed_text = font.render(f'速度: {game.current_fps:.1f}', True, WHITE)
            screen.blit(score_text, (5, 5))
            screen.blit(speed_text, (5, 30))
        elif game.state == GAME_STATE_OVER:
            # 继续显示游戏画面
            screen.fill(BLACK)
            game.snake.draw(screen)
            game.food.draw(screen)

            # 显示分数
            font = pygame.font.SysFont('arial', 20)
            score_text = font.render(f'分数: {game.score}', True, WHITE)
            screen.blit(score_text, (5, 5))

            # 显示游戏结束界面
            game.draw_game_over_screen(screen)

        pygame.display.update()
        clock.tick(game.current_fps if game.state == GAME_STATE_PLAYING else 60)

if __name__ == "__main__":
    main()