import pygame
import random
import sys

# 初始化pygame
pygame.init()

# 颜色定义
BLACK = (0, 0, 0)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
WHITE = (255, 255, 255)

# 游戏设置
WIDTH, HEIGHT = 600, 400
GRID_SIZE = 20
FPS = 10

# 创建屏幕
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption('贪食蛇')
clock = pygame.time.Clock()

# 蛇类
class Snake:
    def __init__(self):
        self.positions = [(WIDTH//2, HEIGHT//2)]
        self.direction = (GRID_SIZE, 0)
        self.length = 1
        
    def get_head_position(self):
        return self.positions[0]
        
    def move(self):
        head = self.get_head_position()
        x, y = self.direction
        new_position = ((head[0] + x) % WIDTH, (head[1] + y) % HEIGHT)
        
        if new_position in self.positions[1:]:
            return False  # 游戏结束
            
        self.positions.insert(0, new_position)
        if len(self.positions) > self.length:
            self.positions.pop()
        return True
        
    def draw(self, surface):
        for position in self.positions:
            rect = pygame.Rect(position[0], position[1], GRID_SIZE, GRID_SIZE)
            pygame.draw.rect(surface, GREEN, rect)
            
# 食物类
class Food:
    def __init__(self):
        self.position = (0, 0)
        self.randomize_position()
        
    def randomize_position(self):
        self.position = (
            random.randint(0, (WIDTH-GRID_SIZE)//GRID_SIZE) * GRID_SIZE,
            random.randint(0, (HEIGHT-GRID_SIZE)//GRID_SIZE) * GRID_SIZE
        )
        
    def draw(self, surface):
        rect = pygame.Rect(self.position[0], self.position[1], GRID_SIZE, GRID_SIZE)
        pygame.draw.rect(surface, RED, rect)

# 主游戏函数
def main():
    snake = Snake()
    food = Food()
    score = 0
    
    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP and snake.direction != (0, GRID_SIZE):
                    snake.direction = (0, -GRID_SIZE)
                elif event.key == pygame.K_DOWN and snake.direction != (0, -GRID_SIZE):
                    snake.direction = (0, GRID_SIZE)
                elif event.key == pygame.K_LEFT and snake.direction != (GRID_SIZE, 0):
                    snake.direction = (-GRID_SIZE, 0)
                elif event.key == pygame.K_RIGHT and snake.direction != (-GRID_SIZE, 0):
                    snake.direction = (GRID_SIZE, 0)
        
        # 移动蛇
        if not snake.move():
            break  # 游戏结束
            
        # 检查是否吃到食物
        if snake.get_head_position() == food.position:
            snake.length += 1
            score += 1
            food.randomize_position()
            
        # 绘制
        screen.fill(BLACK)
        snake.draw(screen)
        food.draw(screen)
        
        # 显示分数
        font = pygame.font.SysFont('arial', 20)
        text = font.render(f'分数: {score}', True, WHITE)
        screen.blit(text, (5, 5))
        
        pygame.display.update()
        clock.tick(FPS)
    
    # 游戏结束显示
    font = pygame.font.SysFont('arial', 30)
    text = font.render(f'游戏结束! 最终分数: {score}', True, WHITE)
    screen.blit(text, (WIDTH//2 - 150, HEIGHT//2 - 15))
    pygame.display.update()
    
    # 等待退出
    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT or event.type == pygame.KEYDOWN:
                pygame.quit()
                sys.exit()

if __name__ == "__main__":
    main()